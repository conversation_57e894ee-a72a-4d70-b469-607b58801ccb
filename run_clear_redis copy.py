#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Redis集群快速清理脚本
用于测试环境的数据清理
"""

import redis
from redis.cluster import RedisCluster, ClusterNode

def quick_clear():
    """快速清理Redis集群数据"""
    
    # 集群配置
    startup_nodes = [
        ClusterNode("*************", 6001),
        ClusterNode("************", 6001),
        ClusterNode("************", 6002),
    ]
    
    try:
        print("🔄 连接Redis集群...")
        redis_client = RedisCluster(
            startup_nodes=startup_nodes, 
            decode_responses=True,
            skip_full_coverage_check=True
        )
        
        redis_client.ping()
        print("✅ 连接成功")
        
        # 统计数据 - 使用更简单的方法
        print("📊 统计数据量...")
        
        # 方法1: 直接对每个配置的节点进行操作
        total_keys = 0
        active_nodes = []
        
        for node in startup_nodes:
            try:
                single_node = redis.Redis(
                    host=node.host, 
                    port=node.port, 
                    decode_responses=True
                )
                
                # 测试连接
                single_node.ping()
                
                # 获取数据库大小
                db_size = single_node.dbsize()
                total_keys += db_size
                active_nodes.append({
                    'host': node.host,
                    'port': node.port,
                    'keys': db_size
                })
                
                print(f"  - {node.host}:{node.port}: {db_size} keys")
                
            except Exception as e:
                print(f"  - {node.host}:{node.port}: 连接失败 ({e})")
        
        print(f"总计: {total_keys} keys")
        
        if total_keys == 0:
            print("✅ 集群已为空")
            return True
        
        if not active_nodes:
            print("❌ 没有可用的节点")
            return False
        
        # 执行清理
        print("🗑️  执行清理...")
        cleared_count = 0
        
        for node_info in active_nodes:
            try:
                single_node = redis.Redis(
                    host=node_info['host'], 
                    port=node_info['port'], 
                    decode_responses=True
                )
                
                # 执行清理
                single_node.flushall()
                cleared_count += 1
                print(f"✅ {node_info['host']}:{node_info['port']} 清理完成")
                
            except Exception as e:
                print(f"❌ {node_info['host']}:{node_info['port']} 清理失败: {e}")
        
        if cleared_count > 0:
            print(f"🎉 清理完成! ({cleared_count}/{len(active_nodes)} 个节点)")
            return True
        else:
            print("❌ 清理失败")
            return False
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("Redis集群快速清理工具")
    print("=" * 50)
    
    confirm = input("确认清理所有数据? (y/N): ")
    if confirm.lower() in ['y', 'yes']:
        quick_clear()
    else:
        print("❌ 操作取消") 