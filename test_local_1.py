import os
import re
import redis
import json
from rediscluster import Redis<PERSON><PERSON>

from redisearch import Client,Query
from redisearch import IndexDefinition
from redisearch.client import IndexType
import redisearch
import get_info_xml as e

# Define the log directory path
log_directory = 'G:\\logs'  # Replace with your log file directory path

# Define the regex pattern to match log content
log_pattern = re.compile(r'(\d{8}-\d{6}),\d+<(\w+)\s*:\d+>:\s*(.*)')

# Define startup nodes

startup_nodes= [
    {'host': '************', 'port': 6004},
]

redis_password = "test123"
try:
    # Create a RedisCluster client
    # redis_client = RedisCluster(startup_nodes=startup_nodes, decode_responses=True, password=redis_password)
    # redis_client = RedisCluster(startup_nodes=startup_nodes, decode_responses=True)
    redis_client = redis.Redis(host="************",port=6004)

    redis_client.ping()
    print("Connected to Red<PERSON>")
except redis.ConnectionError as e:
    print(f"Could not connect to Redis: {e}")

# Function to process a single log file and store it in Redis
def process_log_file():
    count = 0
    # datas = e.getinfo()
    datas = [{"version":1,"filelist":"a.txt"},{"version":2,"filelist":"b.txt"},{"version":3,"filelist":"c.txt"},{"version":2,"filelist":"a.txt"}]
    pipeline = redis_client.pipeline()
    i = 0
    for data in datas:
        i = i + 1
        version = data["version"]
        redis_key = f"log_json_v1:{i}"
        # pipeline.json().set(redis_key, "$", data)
        data["key"] = redis_key
        json_data = json.dumps(data)
        pipeline.execute_command('JSON.SET', redis_key, '$', json_data)
        count += 1
        if count >= 10000:
            pipeline.execute()
            # print(f"Stored log entry in Redis: {data}")
            count = 0
            pipeline = redis_client.pipeline()
            return
    if count > 0:
        pipeline.execute()

# Function to process all .log files in a directory
def process_all_logs(directory):
    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith('.log'):
                file_path = os.path.join(root, file)
                process_log_file(file_path)

def create_index():
    # Define the schema for JSON fields using JSON paths (e.g., $.version, $.time)
    schema = (
        redisearch.NumericField("$.version", as_name="version",sortable=True),
        # redisearch.TagField("$.time", as_name="time"),            # Text field for time
        # redisearch.TextField("$.actions", as_name="actions"),       # Tag field for actions
        redisearch.TextField("$.filelist", as_name="filelist"),    # Text field for filelist
        # redisearch.TextField("$.msg", as_name="msg"),          # Text field for msg
        # redisearch.TextField("$.key", as_name="key")
    )

    # Create the RedisSearch client
    client = Client('my_index', conn=redis_client)  # Ensure index name matches 'item1'

    # Drop the index if it exists
    try:
        client.drop_index()
    except:
        pass  # Ignore if index doesn't exist

    # Create the index with the schema and prefix for JSON data
    definition = IndexDefinition(prefix=['log_json_v1:'], index_type=IndexType.JSON)

    # Create the index
    res = client.create_index(fields=schema, definition=definition)
    # client.load_document(1)
    # print(f"Index created: {res}")
    # print(client.search("*"))

def get_data():
    if redis_client.ping() :
        print("ok")
    # client = Client('my_index', conn=redis_client)
    # query = Query("*").return_fields()
    # res = client.search(query)
    # res = client.redis.execute_command('JSON.GET', 'log_json_v1:32')
    #
    search = redis_client.execute_command(
        'FT.AGGREGATE', 'my_index', '*',
        'GROUPBY', 1, '@filelist',
        'REDUCE', 'FIRST_VALUE', 1, '@version', 'AS', 'latest_version',
        'SORTBY', 2, '@latest_version', 'DESC',
        'LIMIT', 0, 1000
    )
    count = search[0]
    datas = []
    for i in range(1,len(search),2):
        res = redis_client.execute_command('JSON.GET', f'{search[i]}')
        datas.append(res)

    print(datas)
    pass

# Process all log files in the specified directory
# process_all_logs(log_directory)
process_log_file()
# create_index()
# get_data()
