#!/usr/bin/env python3
"""
SVN Log Writer API 启动脚本
"""

import sys
import os

# 确保当前目录在Python路径中
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

if __name__ == '__main__':
    import uvicorn
    from api_service import app, logger
    
    print("=" * 50)
    print("🚀 SVN Log Writer API 服务启动 (FastAPI)")
    print("=" * 50)
    print("📋 可用端点:")
    print("   GET  /              - 服务信息")
    print("   GET  /status        - 系统状态")
    print("   GET  /write_log     - 执行log写入")
    print("   POST /create_index  - 创建索引")
    print("   GET  /docs          - Swagger UI 文档")
    print("   GET  /redoc         - ReDoc 文档")
    print("")
    print("💡 使用示例:")
    print("   curl http://localhost:5000/")
    print("   curl http://localhost:5000/status")
    print("   curl http://localhost:5000/write_log  # 自动获取增量数据")
    print("")
    print("📚 API文档:")
    print("   Swagger UI: http://localhost:5000/docs")
    print("   ReDoc:      http://localhost:5000/redoc")
    print("")
    print("📝 注意:")
    print("   - 请确保SVN命令行工具已安装且可用")
    print("   - 请确保Redis服务正在运行")
    print("   - 默认服务端口: 5000")
    print("=" * 50)
    
    try:
        uvicorn.run("api_service:app", host="0.0.0.0", port=5000, reload=False)
    except KeyboardInterrupt:
        logger.info("👋 服务已停止")
    except Exception as e:
        logger.error(f"❌ 服务启动失败: {e}")
        sys.exit(1) 