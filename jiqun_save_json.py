import os
import re
import json
from redis.cluster import RedisCluster
from redis.exceptions import ConnectionError
from redis.commands.json.path import Path
from concurrent.futures import ThreadPoolExecutor

log_directory = 'G:\\logs'
log_pattern = re.compile(r'(\d{8}-\d{6}),\d+<(\w+)\s*:\d+>:\s*(.*)')

try:
    startup_nodes = [
        {"host": "************", "port": "6001"},
        {"host": "************", "port": "6002"},
        {"host": "************", "port": "6003"}
    ]

    redis_client = RedisCluster(startup_nodes=startup_nodes, decode_responses=True, password="test123")
    redis_client.ping()
    print("Connected to Redis Cluster")
except ConnectionError as e:
    print(f"Could not connect to Redis: {e}")


def process_log_file(file_path):
    with open(file_path, 'r', encoding='gbk') as file:
        filename = os.path.basename(file_path).split('_')[0]

        # 创建一个管道对象
        pipe = redis_client.pipeline(transaction=False)
        count = 0
        for line in file:
            match = log_pattern.match(line.strip())
            if match:
                timestamp, level, content = match.groups()
                log_entry = {
                    "timestamp": timestamp,
                    "level": level,
                    "content": content
                }
                redis_key = f"log:{filename}:{timestamp}"

                # 将日志条目字段存储到 Redis 哈希中
                pipe.hset(redis_key, mapping=log_entry)  # 注意：hmset 在 Redis 5.0 中已弃用，但仍可用
                count += 1

                # 当积累了10000条命令后执行管道操作
                if count % 10000 == 0:
                    pipe.execute()

        # 确保剩余的命令也被执行
        pipe.execute()


def process_all_logs(directory):
    with ThreadPoolExecutor() as executor:
        for root, _, files in os.walk(directory):
            for file in files:
                if file.endswith('.log'):
                    file_path = os.path.join(root, file)
                    # 将日志文件处理工作提交给线程池
                    executor.submit(process_log_file, file_path)
    print("finish")


process_all_logs(log_directory)