import os
import re
import redis
from redis.cluster import Redis<PERSON>luster
from redis.cluster import C<PERSON><PERSON>ode

from redis.commands.json.path import Path

# Define the log directory path
log_directory = 'G:\\logs'  # Replace with your log file directory path

# Define the regex pattern to match log content
log_pattern = re.compile(r'(\d{8}-\d{6}),\d+<(\w+)\s*:\d+>:\s*(.*)')

# Define startup nodes

startup_nodes = [
    ClusterNode("10.11.185.73", 6001),
    ClusterNode("10.11.185.87", 6002),
    ClusterNode("10.11.185.89", 6003),
]
redis_password = "test123"
try:
    # Create a RedisCluster client
    redis_client = RedisCluster(startup_nodes=startup_nodes, decode_responses=True,password=redis_password)

    redis_client.ping()
    print("Connected to Redis")
except redis.ConnectionError as e:
    print(f"Could not connect to Redis: {e}")

# Function to process a single log file and store it in Redis
def process_log_file(file_path):
    with open(file_path, 'r', encoding='gbk') as file:
        filename = os.path.basename(file_path).split('_')[0]  # Extract the part before the underscore from the filename
        count = 0
        pipeline = redis_client.pipeline()
        for line in file:
            match = log_pattern.match(line.strip())
            if match:
                timestamp, level, content = match.groups()
                log_entry = {
                    "content": content,
                    "timestamp": timestamp,
                    "level": level,
                }
                # Use timestamp and filename as the key, store log entry in Redis
                redis_key = f"log_json:{filename}:{timestamp}"

                # Store JSON object
                pipeline.json().set(redis_key, Path.root_path(), log_entry)
                count += 1
                if count >= 10000:
                    pipeline.execute()
                    # print(f"Stored log entry in Redis: {log_entry}")
                    count = 0
                    pipeline = redis_client.pipeline()
                    return
        if count > 0:
            pipeline.execute()
            # print(f"Stored remaining log entries in Redis")

# Function to process all .log files in a directory
def process_all_logs(directory):
    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith('.log'):
                file_path = os.path.join(root, file)
                process_log_file(file_path)

# Process all log files in the specified directory
process_all_logs(log_directory)
