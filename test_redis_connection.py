#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Redis集群连接测试工具
用于诊断Redis连接问题和验证配置
"""

import os
import sys
from redis.cluster import RedisCluster, ClusterNode
import redis
from config import config

def test_single_node(host, port, password=None):
    """测试单个Redis节点连接"""
    print(f"\n🔍 测试单节点连接: {host}:{port}")
    
    try:
        # 创建单节点Redis连接
        client_config = {
            "host": host,
            "port": port,
            "decode_responses": True,
            "socket_connect_timeout": 3,
        }
        
        if password and password.strip():
            client_config["password"] = password
            print(f"   使用密码: {password}")
        else:
            print("   无密码连接")
            
        client = redis.Redis(**client_config)
        
        # 测试连接
        result = client.ping()
        print(f"   ✅ 连接成功: {result}")
        
        # 获取Redis信息
        info = client.info("server")
        print(f"   📊 Redis版本: {info.get('redis_version', 'Unknown')}")
        print(f"   📊 模式: {info.get('redis_mode', 'Unknown')}")
        
        return True
        
    except redis.AuthenticationError as e:
        print(f"   ❌ 认证失败: {e}")
        print("   💡 建议: 检查密码配置或尝试无密码连接")
        return False
        
    except redis.ConnectionError as e:
        print(f"   ❌ 连接失败: {e}")
        print("   💡 建议: 检查主机地址和端口是否正确")
        return False
        
    except Exception as e:
        print(f"   ❌ 其他错误: {e}")
        return False

def test_cluster_connection():
    """测试Redis集群连接"""
    print(f"\n🎯 测试Redis集群连接")
    print(f"配置信息:")
    print(f"   节点: {[(node['host'], node['port']) for node in config.REDIS_CLUSTER_NODES]}")
    print(f"   密码: {'是' if config.REDIS_PASSWORD and config.REDIS_PASSWORD.strip() else '否'}")
    
    try:
        # 使用配置的参数创建集群连接
        redis_client_config = config.get_redis_client_config()
        print(f"   连接参数: {redis_client_config}")
        
        redis_client = RedisCluster(**redis_client_config)
        
        # 测试连接
        result = redis_client.ping()
        print(f"   ✅ 集群连接成功: {result}")
        
        # 获取集群信息
        nodes = redis_client.get_nodes()
        print(f"   📊 集群节点数量: {len(nodes)}")
        
        for node in nodes:
            print(f"   📍 节点: {node.host}:{node.port}")
            
        return True
        
    except redis.exceptions.RedisClusterException as e:
        print(f"   ❌ 集群连接失败: {e}")
        if "AUTH" in str(e):
            print("   💡 这是密码认证问题!")
            return False
        return False
        
    except Exception as e:
        print(f"   ❌ 其他错误: {e}")
        return False

def diagnose_redis_config():
    """诊断Redis配置"""
    print("🔧 Redis配置诊断")
    print("=" * 50)
    
    # 打印当前配置
    config.print_config()
    
    # 1. 测试各个节点
    print(f"\n📋 第一步: 测试各个节点连接")
    successful_nodes = []
    
    for i, node_config in enumerate(config.REDIS_CLUSTER_NODES, 1):
        host = node_config["host"]
        port = node_config["port"]
        
        # 先测试无密码连接
        print(f"\n--- 节点 {i}: {host}:{port} (无密码) ---")
        if test_single_node(host, port, None):
            successful_nodes.append((host, port, None))
        else:
            # 如果无密码失败，尝试有密码连接
            if config.REDIS_PASSWORD:
                print(f"\n--- 节点 {i}: {host}:{port} (有密码) ---")
                if test_single_node(host, port, config.REDIS_PASSWORD):
                    successful_nodes.append((host, port, config.REDIS_PASSWORD))
    
    # 2. 测试集群连接
    print(f"\n📋 第二步: 测试集群连接")
    cluster_success = test_cluster_connection()
    
    # 3. 提供建议
    print(f"\n💡 诊断结果和建议")
    print("=" * 50)
    
    if successful_nodes:
        print(f"✅ 成功连接的节点数量: {len(successful_nodes)}")
        for host, port, password in successful_nodes:
            pwd_status = "有密码" if password else "无密码"
            print(f"   - {host}:{port} ({pwd_status})")
    else:
        print("❌ 所有节点连接失败")
        
    if cluster_success:
        print("✅ 集群连接成功")
    else:
        print("❌ 集群连接失败")
        
        if successful_nodes:
            print("\n🔧 建议的解决方案:")
            
            # 检查密码一致性
            passwords = set(pwd for _, _, pwd in successful_nodes)
            if len(passwords) == 1:
                recommended_password = list(passwords)[0]
                if recommended_password is None:
                    print("1. 设置环境变量: export REDIS_PASSWORD=")
                    print("   或者在env配置文件中注释掉REDIS_PASSWORD行")
                else:
                    print(f"2. 设置环境变量: export REDIS_PASSWORD={recommended_password}")
            else:
                print("⚠️  不同节点使用了不同的密码配置，这不正常!")
                
        else:
            print("\n🔧 无法连接任何节点，请检查:")
            print("1. 网络连接是否正常")
            print("2. Redis服务是否运行")
            print("3. 主机地址和端口是否正确")

def main():
    """主函数"""
    print("🚀 Redis集群连接诊断工具")
    print("=" * 50)
    
    # 运行诊断
    diagnose_redis_config()

if __name__ == "__main__":
    main() 