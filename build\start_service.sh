#!/bin/bash
# 生产环境启动脚本

# 检查Python虚拟环境
if [ ! -d "venv" ]; then
    echo "🔧 创建Python虚拟环境..."
    python3 -m venv venv
fi

# 激活虚拟环境
source venv/bin/activate

# 安装依赖
echo "📦 安装Python依赖..."
pip install -r requirements.txt

# 检查配置文件
if [ ! -f "env_config.env" ]; then
    echo "⚠️  配置文件不存在，使用默认配置..."
    cp production.env env_config.env
    echo "💡 请编辑 env_config.env 文件以设置正确的配置"
fi

# 运行Redis连接测试
echo "🔍 测试Redis连接..."
python test_redis_connection.py

# 启动API服务
echo "🚀 启动API服务..."
python api_service.py
