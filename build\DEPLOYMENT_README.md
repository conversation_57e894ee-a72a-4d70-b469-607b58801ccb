# SVN Log Writer API - 生产环境部署包

## 📋 包含文件
- api_service.py: 主API服务程序
- save_json_local.py: Redis操作和日志处理核心模块
- get_info_xml.py: XML解析工具模块
- config.py: 统一配置管理
- requirements.txt: Python依赖包清单
- env_config.env: 环境配置文件
- production.env: 生产环境配置模板
- test_redis_connection.py: Redis连接诊断工具
- DEPLOYMENT_GUIDE.md: 详细部署指南
- start_service.sh: Linux/Mac启动脚本
- start_service.bat: Windows启动脚本

## 🚀 快速部署

### Linux/Mac:
```bash
chmod +x start_service.sh
./start_service.sh
```

### Windows:
```cmd
start_service.bat
```

## ⚙️ 手动部署

1. 安装Python 3.8+
2. 创建虚拟环境: `python3 -m venv venv`
3. 激活虚拟环境: `source venv/bin/activate` (Linux/Mac) 或 `venv\Scripts\activate` (Windows)
4. 安装依赖: `pip install -r requirements.txt`
5. 编辑配置文件: `vim env_config.env`
6. 测试连接: `python test_redis_connection.py`
7. 启动服务: `python api_service.py`

## 📝 重要提醒

1. **配置文件**: 请根据生产环境修改 `env_config.env` 中的配置
2. **Redis连接**: 启动前请确保Redis集群可访问
3. **SVN服务**: 请确保SVN服务器地址正确
4. **端口开放**: 确保API端口(默认5000)未被占用
5. **日志目录**: 确保LOG_DIRECTORY路径存在且有写入权限

## 🔗 API端点

- 服务状态: http://localhost:5000/status
- API文档: http://localhost:5000/docs
- 更新日志: POST http://localhost:5000/update_log

构建时间: 2025-07-28 14:59:20
