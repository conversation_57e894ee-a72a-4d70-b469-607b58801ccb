import subprocess
import redis
from redis.cluster import RedisCluster
from redis.cluster import <PERSON>lusterNode


def count_svn_files(directory):
    """统计SVN目录下的文件数量"""
    try:
        # Run the svn list command recursively
        result = subprocess.run(
            ['svn', 'list', '-R', directory],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )

        if result.returncode != 0:
            print(f"Error running svn command: {result.stderr}")
            return 0

        # Count the number of lines in the output, each line corresponds to a file or directory
        lines = result.stdout.splitlines()
        # 过滤掉目录（以/结尾的行），只统计文件
        # files = [line for line in lines if not line.endswith('/')]
        files = [line for line in lines ] 
        return len(files)

    except Exception as e:
        print(f"Error: {e}")
        return 0


def count_redis_data():
    """统计Redis中存储的数据 - 按文件最新状态统计"""
    startup_nodes = [
        ClusterNode("************", 6001),
        ClusterNode("************", 6002),
        ClusterNode("************", 6003),
    ]
    
    try:
        redis_client = RedisCluster(startup_nodes=startup_nodes, decode_responses=True)
        redis_client.ping()
        print("Connected to Redis")
        
        # 获取所有匹配的键
        keys = []
        for node in redis_client.get_nodes():
            node_keys = node.redis_connection.keys('log_json_v1:*')
            keys.extend(node_keys)
        
        total_records = len(keys)
        print(f"正在分析 {total_records} 条记录...")
        
        # 按文件路径分组，记录每个文件的所有版本
        file_versions = {}  # {filelist: [(version, exist, data), ...]}
        
        # 读取所有记录并按文件分组
        for i, key in enumerate(keys):
            try:
                data = redis_client.json().get(key)
                if data:
                    filelist = data.get('filelist', '')
                    version = int(data.get('version', 0))
                    exist = data.get('exist', '1')
                    
                    if filelist not in file_versions:
                        file_versions[filelist] = []
                    
                    file_versions[filelist].append((version, exist, data))
                    
            except Exception as e:
                print(f"Error reading key {key}: {e}")
            
            # 显示进度
            if (i + 1) % 100000 == 0:
                print(f"已处理 {i + 1}/{total_records} 条记录...")
        
        # 分析每个文件的最新状态
        active_files = 0  # exist="1"的文件数量
        deleted_files = 0  # exist="0"的文件数量
        versions_stat = {}  # 版本统计
        
        for filelist, versions in file_versions.items():
            # 按版本号排序，取最新版本的状态
            versions.sort(key=lambda x: x[0], reverse=True)  # 按version降序
            latest_version, latest_exist, latest_data = versions[0]
            
            # 统计版本分布
            version = latest_data.get('version', 0)
            versions_stat[version] = versions_stat.get(version, 0) + 1
            
            # 根据最新状态分类
            if latest_exist == '1':
                active_files += 1
            else:
                deleted_files += 1
        
        return {
            'total_records': total_records,
            'total_unique_files': len(file_versions),
            'active_files': active_files,  # 这个才是应该与SVN对比的数量
            'deleted_files': deleted_files,
            'versions_distribution': versions_stat
        }
        
    except Exception as e:
        print(f"Redis connection error: {e}")
        return None


def count_redis_data_with_search():
    """使用Redis搜索功能统计数据（备用方案）"""
    startup_nodes = [
        ClusterNode("************", 6001),
        ClusterNode("************", 6002),
        ClusterNode("************", 6003),
    ]
    
    try:
        redis_client = RedisCluster(startup_nodes=startup_nodes, decode_responses=True)
        redis_client.ping()
        
        # 使用FT.SEARCH查询存在的文件
        try:
            # 搜索exist="1"的记录
            active_result = redis_client.execute_command(
                'FT.SEARCH', 'my_index', '@exist:1', 'LIMIT', '0', '0'
            )
            active_count = active_result[0] if active_result else 0
            
            # 搜索exist="0"的记录  
            deleted_result = redis_client.execute_command(
                'FT.SEARCH', 'my_index', '@exist:0', 'LIMIT', '0', '0'
            )
            deleted_count = deleted_result[0] if deleted_result else 0
            
            # 搜索所有记录
            total_result = redis_client.execute_command(
                'FT.SEARCH', 'my_index', '*', 'LIMIT', '0', '0'
            )
            total_count = total_result[0] if total_result else 0
            
            return {
                'total_records': total_count,
                'active_records': active_count,
                'deleted_records': deleted_count,
                'note': '注意：这只是记录数，不是文件数（一个文件可能有多个版本记录）'
            }
            
        except Exception as e:
            print(f"搜索查询失败: {e}")
            return None
            
    except Exception as e:
        print(f"Redis connection error: {e}")
        return None


def debug_redis_index():
    """调试Redis索引配置和数据结构"""
    startup_nodes = [
        ClusterNode("************", 6001),
        ClusterNode("************", 6002),
        ClusterNode("************", 6003),
    ]
    
    try:
        redis_client = RedisCluster(startup_nodes=startup_nodes, decode_responses=True)
        redis_client.ping()
        print("Connected to Redis")
        
        print("\n=== Redis索引调试信息 ===")
        
        # 1. 检查索引是否存在
        try:
            index_info = redis_client.execute_command('FT.INFO', 'my_index')
            print("✅ 索引存在")
            print(f"索引信息: {index_info[:10]}...")  # 显示前10个元素
        except Exception as e:
            print(f"❌ 索引不存在或有问题: {e}")
            return
        
        # 2. 测试基础搜索
        try:
            basic_search = redis_client.execute_command('FT.SEARCH', 'my_index', '*', 'LIMIT', '0', '3')
            print(f"✅ 基础搜索成功，找到 {basic_search[0]} 条记录")
            
            if len(basic_search) > 2:
                print("🔍 样本记录结构:")
                sample_record = basic_search[2]  # 第一条记录的字段
                for i in range(0, min(len(sample_record), 10), 2):
                    if i + 1 < len(sample_record):
                        print(f"   {sample_record[i]}: {sample_record[i + 1]}")
        except Exception as e:
            print(f"❌ 基础搜索失败: {e}")
            return
            
        # 3. 检查字段是否可以聚合
        print("\n🔍 字段聚合测试:")
        test_fields = ['filelist', 'version', 'exist']
        
        for field in test_fields:
            try:
                # 测试简单聚合
                agg_result = redis_client.execute_command(
                    'FT.AGGREGATE', 'my_index',
                    '*',
                    'GROUPBY', '1', f'@{field}',
                    'REDUCE', 'COUNT', '0',
                    'LIMIT', '0', '5'
                )
                if agg_result and len(agg_result) > 0:
                    print(f"   ✅ {field}: 可聚合，{agg_result[0]} 个不同值")
                else:
                    print(f"   ❌ {field}: 聚合返回空")
            except Exception as e:
                print(f"   ❌ {field}: 聚合失败 - {e}")
        
        # 4. 测试修复的聚合查询
        print("\n🔧 测试修复的聚合查询:")
        try:
            # 使用正确的字段名（可能需要调整）
            result = redis_client.execute_command(
                'FT.AGGREGATE', 'my_index',
                '*',
                'GROUPBY', '1', '$.filelist',  # 尝试JSON路径
                'REDUCE', 'MAX', '1', '$.version', 'AS', 'max_version',
                'REDUCE', 'COUNT', '0', 'AS', 'record_count',
                'LIMIT', '0', '10'
            )
            print(f"✅ JSON路径聚合成功: {len(result)} 个结果")
            return result
        except Exception as e:
            print(f"❌ JSON路径聚合失败: {e}")
            
        # 5. 尝试别名字段
        try:
            result = redis_client.execute_command(
                'FT.AGGREGATE', 'my_index',
                '*',
                'GROUPBY', '1', '@filelist',  # 使用别名
                'REDUCE', 'MAX', '1', '@version', 'AS', 'max_version',
                'REDUCE', 'COUNT', '0', 'AS', 'record_count',
                'LIMIT', '0', '10'
            )
            print(f"✅ 别名字段聚合成功: {len(result)} 个结果")
            return result
        except Exception as e:
            print(f"❌ 别名字段聚合失败: {e}")
            
        return None
        
    except Exception as e:
        print(f"调试连接失败: {e}")
        return None


def count_redis_data_with_sql_like_query_fixed():
    """修复的SQL风格查询"""
    startup_nodes = [
        ClusterNode("************", 6001),
        ClusterNode("************", 6002),
        ClusterNode("************", 6003),
    ]
    
    try:
        redis_client = RedisCluster(startup_nodes=startup_nodes, decode_responses=True)
        redis_client.ping()
        print("Connected to Redis")
        
        try:
            print("🔄 使用修复后的SQL风格查询...")
            
            # 使用正确的字段引用语法（基于调试结果）
            result = redis_client.execute_command(
                'FT.AGGREGATE', 'my_index',
                '*',
                'GROUPBY', '1', '@filelist',  # 使用别名字段
                'REDUCE', 'MAX', '1', '@version', 'AS', 'max_version',  # NUMERIC字段的MAX聚合
                'REDUCE', 'COUNT', '0', 'AS', 'record_count',
                'LIMIT', '0', '100000'
            )
            
            if not result or len(result) < 1 or result[0] == 0:
                print("❌ 聚合查询返回空结果")
                return None
            
            total_files = result[0]
            print(f"✅ 聚合成功！找到 {total_files} 个唯一文件")
            
            # 处理聚合结果
            active_files = 0
            deleted_files = 0
            total_records = 0
            version_stats = {}
            
            print("正在查询每个文件的最新状态...")
            
            # 解析聚合结果
            for i in range(1, len(result), 2):
                if i + 1 >= len(result):
                    break
                    
                # 解析文件组信息
                file_info = result[i + 1]
                filelist = None
                max_version = 0
                record_count = 0
                
                # 解析字段值
                for j in range(0, len(file_info), 2):
                    if j + 1 < len(file_info):
                        field_name = file_info[j]
                        field_value = file_info[j + 1]
                        
                        if field_name == 'filelist':
                            filelist = field_value
                        elif field_name == 'max_version':
                            max_version = int(field_value)
                        elif field_name == 'record_count':
                            record_count = int(field_value)
                
                if filelist and max_version > 0:
                    total_records += record_count
                    version_stats[max_version] = version_stats.get(max_version, 0) + 1
                    
                    # 查询该文件最新版本的exist状态
                    # 使用修复后的字段引用
                    try:
                        search_result = redis_client.execute_command(
                            'FT.SEARCH', 'my_index',
                            f'@filelist:"{filelist}" @version:[{max_version} {max_version}]',  # NUMERIC字段范围查询
                            'LIMIT', '0', '1'
                        )
                        
                        if search_result and len(search_result) > 2:
                            # 解析exist状态
                            record_fields = search_result[2]
                            exist_status = "1"  # 默认值
                            
                            # 查找exist字段
                            for k in range(0, len(record_fields), 2):
                                if k + 1 < len(record_fields):
                                    if record_fields[k] == '$.exist':
                                        exist_status = record_fields[k + 1]
                                        break
                            
                            if exist_status == "1":
                                active_files += 1
                            else:
                                deleted_files += 1
                        else:
                            # 如果查询失败，假设文件存在
                            active_files += 1
                            
                    except Exception as e:
                        print(f"查询文件 {filelist} 状态失败: {e}")
                        active_files += 1  # 默认假设存在
                
                # 显示进度
                if (i // 2) % 100 == 0:
                    print(f"已处理 {i // 2}/{total_files} 个文件组...")
            
            return {
                'total_records': total_records,
                'total_unique_files': total_files,
                'active_files': active_files,
                'deleted_files': deleted_files,
                'versions_distribution': version_stats,
                'query_method': 'FT.AGGREGATE + FT.SEARCH (修复版)'
            }
            
        except Exception as e:
            print(f"SQL风格查询失败: {e}")
            return None
            
    except Exception as e:
        print(f"Redis connection error: {e}")
        return None


def count_redis_data_optimized():
    """优化版本：结合聚合查询和批量操作"""
    startup_nodes = [
        ClusterNode("************", 6001),
        ClusterNode("************", 6002),
        ClusterNode("************", 6003),
    ]
    
    try:
        redis_client = RedisCluster(startup_nodes=startup_nodes, decode_responses=True)
        redis_client.ping()
        print("Connected to Redis")
        
        try:
            # 方法1：使用FT.AGGREGATE获取文件分组信息
            print("正在使用SQL风格查询分析数据...")
            sql_result = count_redis_data_with_sql_like_query_fixed()
            
            if sql_result:
                return sql_result
            else:
                raise Exception("聚合查询失败")
                
        except Exception as e:
            print(f"SQL风格查询失败: {e}")
            print("使用传统方法...")
            return count_redis_data()
            
    except Exception as e:
        print(f"Redis connection error: {e}")
        return None


def validate_data_integrity(svn_directory):
    """综合验证数据完整性 - 使用优化的SQL风格查询"""
    print("=" * 60)
    print("📊 数据完整性验证报告")
    print("=" * 60)
    
    # 1. SVN文件统计
    print("\n🔍 1. SVN文件统计")
    # svn_file_count = count_svn_files(svn_directory)
    svn_file_count = 356116
    print(f"   SVN目录: {svn_directory}")
    print(f"   当前存在文件数量: {svn_file_count}")
    
    # 2. Redis数据统计 - 优先使用SQL风格查询
    print("\n🔍 2. Redis数据分析")
    redis_stats = count_redis_data_optimized()
    
    if redis_stats:
        print(f"   查询方法: {redis_stats.get('query_method', 'Unknown')}")
        print(f"   总记录数: {redis_stats['total_records']}")
        print(f"   唯一文件数: {redis_stats['total_unique_files']}")
        print(f"   存在状态文件: {redis_stats['active_files']}")
        print(f"   删除状态文件: {redis_stats['deleted_files']}")
        print(f"   平均每文件记录数: {redis_stats['total_records'] / redis_stats['total_unique_files']:.1f}")
        
        # 版本分布
        if redis_stats['versions_distribution']:
            print(f"   版本分布: {dict(list(redis_stats['versions_distribution'].items())[:5])}...")
        
        # 3. 关键对比分析
        print("\n📈 3. 关键对比分析")
        active_files = redis_stats['active_files']
        
        if svn_file_count > 0:
            # 覆盖率：Redis中存在的文件 vs SVN中的文件
            coverage = (active_files / svn_file_count) * 100
            print(f"   Redis存在文件数: {active_files}")
            print(f"   SVN当前文件数: {svn_file_count}")
            print(f"   覆盖率: {coverage:.1f}%")
            
            if coverage >= 95:
                print("   ✅ 覆盖率优秀 - 数据完整性很好")
            elif coverage >= 80:
                print("   ✅ 覆盖率良好 - 数据基本完整")
            elif coverage >= 60:
                print("   ⚠️  覆盖率中等 - 可能缺少部分文件记录")
            else:
                print("   ❌ 覆盖率较低 - 数据可能不完整")
            
            # 差异分析
            diff = abs(active_files - svn_file_count)
            diff_rate = (diff / svn_file_count) * 100
            print(f"   文件数量差异: {diff} ({diff_rate:.1f}%)")
            
            if diff_rate <= 5:
                print("   ✅ 数量差异很小")
            elif diff_rate <= 15:
                print("   ⚠️  有一定差异，可能是正常的版本差异")
            else:
                print("   ❌ 差异较大，需要检查数据质量")
    
    # 4. 详细建议
    print("\n💡 4. 数据质量评估")
    if redis_stats and svn_file_count > 0:
        total_records = redis_stats['total_records']
        unique_files = redis_stats['total_unique_files']
        active_files = redis_stats['active_files']
        deleted_files = redis_stats['deleted_files']
        
        # 记录密度分析
        record_density = total_records / unique_files
        print(f"   记录密度 (总记录/唯一文件): {record_density:.1f}")
        
        if record_density >= 3:
            print("   ✅ 记录密度高，说明有丰富的历史记录")
        elif record_density >= 2:
            print("   ⚠️  记录密度中等")
        else:
            print("   ❌ 记录密度偏低，可能缺少历史记录")
        
        # 删除率分析
        deletion_rate = (deleted_files / unique_files) * 100
        print(f"   文件删除率: {deletion_rate:.1f}%")
        
        if deletion_rate <= 10:
            print("   ✅ 删除率正常")
        elif deletion_rate <= 30:
            print("   ⚠️  删除率偏高")
        else:
            print("   ❌ 删除率很高，可能数据有问题")
        
        # 性能提示
        query_method = redis_stats.get('query_method', '')
        if 'SQL-like' in query_method:
            print("   🚀 使用了SQL风格查询，性能优化")
        else:
            print("   ⚠️  使用了传统遍历方法，性能一般")
    
    print("\n" + "=" * 60)
    
    return {
        'svn_files': svn_file_count,
        'redis_stats': redis_stats
    }


# 使用示例
if __name__ == "__main__":
    import sys
    
    # 检查是否要运行调试
    if len(sys.argv) > 1 and sys.argv[1] == 'debug':
        print("🔧 运行Redis索引调试...")
        debug_result = debug_redis_index()
        if debug_result:
            print(f"调试结果: {debug_result}")
    else:
        # 验证数据完整性
        directory_path = 'D:\ygame_code'
        result = validate_data_integrity(directory_path)
        
        # 单独统计（保持原有功能）
        # svn_file_count = count_svn_files(directory_path)
        # print(f"\n📁 SVN文件总数: {svn_file_count}")
