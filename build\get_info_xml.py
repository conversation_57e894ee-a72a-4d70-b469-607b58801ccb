import xml.etree.ElementTree as ET
from datetime import datetime
import pytz
import os

def getinfo(xml_file_path="log.xml"):
    # 检查文件是否存在
    if not os.path.exists(xml_file_path):
        print(f"Error: XML file not found: {xml_file_path}")
        return []
    
    try:
        with open(xml_file_path, 'r', encoding="utf-8") as f:
            xml_data = f.read()
        print(f"Successfully loaded XML file: {xml_file_path}")
    except Exception as e:
        print(f"Error reading XML file {xml_file_path}: {e}")
        return []

    # Parse the XML data
    root = ET.fromstring(xml_data)

    # Function to convert UTC time to timestamp
    def convert_utc_to_timestamp(utc_time_str):
        try:
            utc_format = "%Y-%m-%dT%H:%M:%S.%fZ"  # Format of the date in the XML
            # Parse the UTC time string
            utc_time = datetime.strptime(utc_time_str, utc_format)
            # Set timezone for UTC
            utc_time = utc_time.replace(tzinfo=pytz.UTC)
            # Convert to timestamp
            return int(utc_time.timestamp())
        except Exception as e:
            print(f"Warning: Error converting date {utc_time_str} to timestamp: {e}")
            return int(datetime(1970, 1, 1).timestamp())  # 返回Unix时间戳起点

    # Function to parse individual log entries and convert them to a dictionary
    def parse_log_entry(logentry, json_save):
        version = logentry.attrib.get('revision', 'v1.0')
        
        # 防御性编程：检查date元素是否存在
        date_element = logentry.find('date')
        if date_element is not None:
            date_utc = date_element.text
        else:
            print(f"Warning: No date element found in logentry revision {version}")
            date_utc = "1970-01-01T00:00:00.000Z"  # 默认时间
        
        # 防御性编程：检查msg元素是否存在
        msg_element = logentry.find('msg')
        missing_msg = False
        if msg_element is not None:
            msg = msg_element.text if msg_element.text else ""  # 处理空文本情况
        else:
            missing_msg = True
            msg = "No message available"  # 默认消息

        # Convert UTC time to timestamp
        timestamp = convert_utc_to_timestamp(date_utc)

        # Extract the filelist and actions
        status = 0
        
        # 防御性编程：检查paths元素是否存在
        paths_element = logentry.find('paths')
        if paths_element is None:
            print(f"Warning: No paths element found in logentry revision {version}")
            return json_save
        
        # 统计这个logentry下的path数量
        path_count = len(paths_element.findall('path'))
        if missing_msg and path_count > 0:
            print(f"Warning: No msg element found in logentry revision {version}, affecting {path_count} file paths")
        
        for path in paths_element.findall('path'):

            filepath = path.text
            if "Jx3mHelperTray/Commands/Projects/CodeBreak/CodeBreakClientDownloadMenu.cs" in filepath:
                print(1)
            now_status = 0
            kind = path.attrib.get('kind', 'file')
            action = path.attrib.get('action', 'M')  # Add, delete, modify actions
            copyfrom_path = path.attrib.get('copyfrom-path', None)

            dir_path = path.text

            if dir_path == "Jx3mHelperTray/Commands":
                print(1)

            if action == 'A':
                now_status = now_status | (1 << 2)
            elif action == 'D':
                now_status = now_status | (1 << 1)
            elif action == 'M':  # Modify
                now_status = now_status | 1
            else:
                now_status = now_status | (1 << 3)

            status = status | now_status #存这个版本的总状态


            if kind == 'dir':
                exist = "1"
                # Handle directory specific logic
                if action == 'D':
                    exist = "0"
                    
                    # 找到所有受影响的文件和子目录，为它们创建删除记录
                    for dir_key in list(json_save.keys()):
                        if dir_path in dir_key:  # 如果是被删除目录的子路径
                            # 为每个受影响的路径创建删除记录
                            json_save.setdefault(dir_key, []).append({
                                "version": int(version),
                                "time": timestamp,
                                "filelist": dir_key,
                                "msg": f"Directory deleted: {dir_path}",
                                "action": str(now_status),
                                "actions": str(status),
                                "exist": "0"  # 新的删除记录
                            })
                    
                    # 为目录本身创建删除记录
                    json_save.setdefault(dir_path, []).append({
                        "version": int(version),
                        "time": timestamp,
                        "filelist": dir_path,
                        "msg": msg,
                        "action": str(now_status),
                        "actions": str(status),
                        "exist": "0"
                    })

                elif action == 'A':
                    # Directory addition, prepare the json structure
                    if copyfrom_path:
                        # 目录复制逻辑修复
                        for dir_key in list(json_save.keys()):
                            if copyfrom_path in dir_key:
                                new_dir_key = dir_key.replace(copyfrom_path, dir_path)
                                
                                # 为源目录创建删除记录，为新目录创建添加记录
                                if dir_key in json_save:
                                    # 获取源目录的最新状态作为模板
                                    latest_entry = json_save[dir_key][-1] if json_save[dir_key] else {}
                                    
                                    # 为源路径创建删除记录（如果需要）
                                    json_save.setdefault(dir_key, []).append({
                                        "version": int(version),
                                        "time": timestamp,
                                        "filelist": dir_key,
                                        "msg": f"Directory moved from {copyfrom_path} to {dir_path}",
                                        "action": str(now_status),
                                        "actions": str(status),
                                        "exist": "0"
                                    })
                                    
                                    # 为新路径创建添加记录
                                    json_save.setdefault(new_dir_key, []).append({
                                        "version": int(version),
                                        "time": timestamp,
                                        "filelist": new_dir_key,
                                        "msg": msg,
                                        "action": str(now_status),
                                        "actions": str(status),
                                        "exist": "1"
                                    })

                    else:
                        # Handle normal directory addition
                        json_save.setdefault(dir_path, []).append({
                            "version": int(version),
                            "time": timestamp,
                            "filelist": dir_path,  # Store full path of the file
                            "msg": msg,
                            "action": str(now_status),
                            "actions": str(status),
                            "exist": "1"
                        })
            else:
                exist = "1"
                if action == "D":
                    exist = "0"
                json_save.setdefault(dir_path, []).append({
                    "version": int(version),
                    "time": timestamp,
                    "filelist": filepath,  # Store full file path here
                    "msg": msg,
                    "action": str(now_status),
                    "actions": str(status),
                    "exist": exist
                })

        return json_save


    # Create a list to hold all parsed log entries
    log_entries = []
    json_save = {}
    delete_queue = []
    # Iterate through each logentry in the XML
    # for logentry in root.findall('logentry'):
    #     if len(log_entries) <= 0:
    #         log_entries = parse_log_entry(logentry)
    #     else:
    #         log_entries = log_entries + parse_log_entry(logentry,json_save,delete_queue)

    for log_entry in reversed(root.findall('logentry')):  # Assuming xml_log_entries is the list of <logentry> elements
        json_save = parse_log_entry(log_entry, json_save )

    # 扁平化数据结构，返回所有数据项的列表
    flattened_result = []
    for path_data in json_save.values():
        if isinstance(path_data, list):
            flattened_result.extend(path_data)
        else:
            flattened_result.append(path_data)

    # Output parsed log entries
    # for entry in log_entries:
    #     print(entry)

    return flattened_result  # 返回扁平化的列表而不是嵌套字典

def get_files_in_directory(dir_path, json_save):
    res = []
    for dir in json_save.keys():
        if dir_path in dir:
            res.append(json_save[dir])
    return res


if __name__ == '__main__':
    res = getinfo()
    print(1)