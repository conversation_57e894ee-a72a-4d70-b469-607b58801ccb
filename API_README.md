# SVN Log Writer API 服务

这是一个用于自动获取SVN日志并写入Redis数据库的API服务，使用FastAPI框架构建。

## 功能特性

- 🔍 自动查询当前记录中最新的log version
- 📥 通过SVN命令自动获取增量日志数据  
- 📊 解析XML格式的SVN日志并写入Redis
- 🔄 支持批量处理和事务操作
- 📈 提供详细的处理统计信息
- 📚 自动生成API文档 (Swagger UI / ReDoc)
- ⚡ 高性能异步处理
- 🔧 类型安全和数据验证

## 系统要求

### 必需组件
- Python 3.7+
- Redis 7.0+ (支持Redis Search)
- SVN命令行工具
- FastAPI Web框架

### Python依赖
```bash
pip install -r requirements.txt
```

## 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 配置Redis连接
确保在 `save_json_local.py` 中正确配置了Redis连接参数：
```python
startup_nodes = [
    ClusterNode("************", 6010),
    ClusterNode("************", 6002),
    ClusterNode("************", 6003),
]
```

### 3. 启动服务
```bash
python start_api.py
```
或者
```bash
python api_service.py
```

## API端点说明

### GET `/`
服务信息和可用端点列表

**响应示例:**
```json
{
  "service": "SVN Log Writer API",
  "version": "2.0.0",
  "framework": "FastAPI",
  "endpoints": {
    "write_log": "/write_log?svn_url=<url>",
    "status": "/status",
    "create_index": "/create_index",
    "home": "/",
    "docs": "/docs",
    "redoc": "/redoc"
  },
  "description": "提供SVN日志自动获取和写入Redis的API服务"
}
```

### GET `/docs`
Swagger UI 交互式API文档
- 提供完整的API文档界面
- 支持在线测试API端点
- 自动生成的请求/响应示例

### GET `/redoc`
ReDoc 风格的API文档
- 美观的文档展示界面
- 详细的API规范说明
- 更适合阅读的文档格式

### GET `/status`
获取系统状态和当前最新版本

**响应示例:**
```json
{
  "success": true,
  "redis_status": "connected",
  "latest_version": 1234,
  "timestamp": "2024-01-15T10:30:00.123456"
}
```

### GET/POST `/write_log`
自动执行增量log写入操作

**功能:**
- 自动查询Redis中的最大版本号
- 从最大版本号+1开始获取SVN增量数据  
- 解析XML并批量写入Redis

**使用示例:**
```bash
# 触发自动增量数据获取
curl http://localhost:5000/write_log
```

**成功响应示例:**
```json
{
  "success": true,
  "message": "Log写入成功完成",
  "latest_version_before": 1234,
  "latest_version_after": 1250,
  "processed_count": 16,
  "svn_url": "yexiao.fun",
  "start_version": 1235,
  "timestamp": "2024-01-15T10:30:00.123456"
}
```

**无新日志响应示例:**
```json
{
  "success": true,
  "message": "没有新的日志记录需要处理",
  "latest_version": 1234,
  "processed_count": 0
}
```

**错误响应示例:**
```json
{
  "success": false,
  "error": "SVN命令执行失败",
  "latest_version": 1234
}
```

### POST `/create_index`
创建或重建Redis搜索索引

**响应示例:**
```json
{
  "success": true,
  "message": "索引创建成功",
  "timestamp": "2024-01-15T10:30:00.123456"
}
```

## 工作流程

1. **查询最新版本**: 自动从Redis数据库查询当前最新的log version
2. **计算起始版本**: 设置起始版本为最新版本+1
3. **执行SVN命令**: 运行 `svn log -r 起始版本:HEAD -v --xml` 获取增量日志
4. **解析XML文件**: 解析SVN输出的XML格式日志数据
5. **写入Redis**: 批量将解析后的数据写入Redis数据库
6. **清理资源**: 删除临时XML文件并返回处理结果

## 配置说明

### SVN命令格式
API使用以下SVN命令格式：
```bash
svn log -r 起始版本号:HEAD -v --xml 输出文件 SVN_URL
```
自动从起始版本号（Redis最大版本号+1）获取到HEAD的所有增量提交记录。

### Redis数据格式
日志数据以JSON格式存储在Redis中，键名格式为：
```
log_json_v1:索引号
```

### 索引配置
支持以下字段的搜索和聚合：
- `version`: 版本号 (NUMERIC)
- `time`: 时间戳 (NUMERIC)  
- `actions`: 操作类型 (TAG)
- `filelist`: 文件列表 (TEXT)
- `msg`: 日志消息 (TEXT)
- `exist`: 存在标志 (TAG)
- `action`: 动作 (TAG)

## 错误处理

### 常见错误类型
- **SVN命令失败**: SVN工具不可用或网络问题
- **Redis连接失败**: Redis服务未启动或配置错误
- **XML解析失败**: SVN输出格式异常
- **权限问题**: 临时文件创建失败

### 故障排除
1. 检查SVN命令行工具是否已安装: `svn --version`
2. 检查Redis服务是否运行: `redis-cli ping`
3. 验证网络连接和SVN仓库权限
4. 查看服务日志获取详细错误信息

## 监控和日志

服务提供详细的日志输出，包括：
- 📍 操作进度指示器
- ⏱️ 执行时间统计
- 📊 数据处理统计
- ⚠️ 错误和警告信息

## 安全注意事项

- API默认绑定到所有网络接口 (0.0.0.0)
- 建议在生产环境中配置防火墙和访问控制
- SVN认证信息需要在系统级别配置
- Redis连接建议使用密码保护

## 开发和扩展

### 代码结构
- `api_service.py`: 主要API服务代码
- `save_json_local.py`: Redis连接和数据处理逻辑
- `get_info_xml.py`: XML解析模块
- `start_api.py`: 服务启动脚本

### 自定义配置
可以通过修改 `api_service.py` 中的配置常量来自定义：
- 默认SVN URL
- 超时设置
- 日志级别
- 服务端口

## 许可证

本项目采用开源许可证，详情请查看LICENSE文件。 