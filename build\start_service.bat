@echo off
REM 生产环境启动脚本 (Windows)

REM 检查Python虚拟环境
if not exist "venv" (
    echo 🔧 创建Python虚拟环境...
    python -m venv venv
)

REM 激活虚拟环境
call venv\Scripts\activate.bat

REM 安装依赖
echo 📦 安装Python依赖...
pip install -r requirements.txt

REM 检查配置文件
if not exist "env_config.env" (
    echo ⚠️  配置文件不存在，使用默认配置...
    copy production.env env_config.env
    echo 💡 请编辑 env_config.env 文件以设置正确的配置
)

REM 运行Redis连接测试
echo 🔍 测试Redis连接...
python test_redis_connection.py

REM 启动API服务
echo 🚀 启动API服务...
python api_service.py

pause
