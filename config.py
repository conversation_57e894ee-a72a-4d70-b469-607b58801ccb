import os
from typing import List, Dict

# 加载env_config.env文件
def load_env_config(file_path="env_config.env"):
    """从指定的env文件加载环境变量"""
    if os.path.exists(file_path):
        loaded_count = 0
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                # 跳过注释和空行
                if line and not line.startswith('#'):
                    if '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip()
                        # 只有在环境变量中不存在时才设置
                        if key not in os.environ:
                            os.environ[key] = value
                            loaded_count += 1
        print(f"✅ 成功加载配置文件: {file_path} (加载了 {loaded_count} 个配置项)")
    else:
        print(f"⚠️ 配置文件不存在: {file_path}")
        print(f"💡 请创建配置文件或使用 cp production.env {file_path}")

def validate_required_env_vars():
    """验证必需的环境变量是否已设置"""
    required_vars = [
        'REDIS_HOST_1', 'REDIS_PORT_1', 'REDIS_HOST_2', 'REDIS_PORT_2', 
        'REDIS_HOST_3', 'REDIS_PORT_3', 'REDIS_SOCKET_TIMEOUT', 'REDIS_SOCKET_KEEPALIVE',
        'SVN_URL', 'SVN_TIMEOUT', 'API_HOST', 'API_PORT', 'API_RELOAD',
        'LOG_DIRECTORY', 'BATCH_SIZE', 'LOG_LEVEL'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ 缺少必需的环境变量: {', '.join(missing_vars)}")
        print(f"💡 请检查 env_config.env 文件是否包含所有必需的配置项")
        return False
    
    return True

# 在类定义前加载环境变量
load_env_config()
validate_required_env_vars()

class Config:
    """统一配置管理类 - 支持环境变量和默认值"""
    
    # Redis 集群配置
    REDIS_CLUSTER_NODES = [
        {
            "host": os.getenv("REDIS_HOST_1"),
            "port": int(os.getenv("REDIS_PORT_1"))
        },
        {
            "host": os.getenv("REDIS_HOST_2"), 
            "port": int(os.getenv("REDIS_PORT_2"))
        },
        {
            "host": os.getenv("REDIS_HOST_3"),
            "port": int(os.getenv("REDIS_PORT_3"))
        }
    ]
    
    # Redis 认证配置
    REDIS_PASSWORD = os.getenv("REDIS_PASSWORD")
    REDIS_SOCKET_TIMEOUT = int(os.getenv("REDIS_SOCKET_TIMEOUT"))
    REDIS_SOCKET_KEEPALIVE = os.getenv("REDIS_SOCKET_KEEPALIVE").lower() == "true"
    
    # SVN 服务器配置
    SVN_URL = os.getenv("SVN_URL")
    SVN_TIMEOUT = int(os.getenv("SVN_TIMEOUT"))  # SVN命令超时时间(秒)
    
    # API 服务配置
    API_HOST = os.getenv("API_HOST")
    API_PORT = int(os.getenv("API_PORT"))
    API_RELOAD = os.getenv("API_RELOAD").lower() == "true"
    
    # 日志和文件路径配置
    LOG_DIRECTORY = os.getenv("LOG_DIRECTORY")
    
    # 批处理配置
    BATCH_SIZE = int(os.getenv("BATCH_SIZE"))
    
    # 日志级别配置
    LOG_LEVEL = os.getenv("LOG_LEVEL")
    
    @classmethod
    def get_redis_startup_nodes(cls):
        """获取Redis集群启动节点配置"""
        from redis.cluster import ClusterNode
        return [
            ClusterNode(node["host"], node["port"]) 
            for node in cls.REDIS_CLUSTER_NODES
        ]
    
    @classmethod
    def get_redis_client_config(cls):
        """获取Redis客户端配置字典"""
        config = {
            "startup_nodes": cls.get_redis_startup_nodes(),
            "decode_responses": True,
            "socket_connect_timeout": cls.REDIS_SOCKET_TIMEOUT,
            "socket_keepalive": cls.REDIS_SOCKET_KEEPALIVE,
        }
        
        # 只有当密码不为空时才添加password参数
        if cls.REDIS_PASSWORD and cls.REDIS_PASSWORD.strip():
            config["password"] = cls.REDIS_PASSWORD
            
        return config
    
    @classmethod
    def print_config(cls):
        """打印当前配置信息 - 用于调试"""
        print("=== 当前配置信息 ===")
        print(f"Redis集群节点: {cls.REDIS_CLUSTER_NODES}")
        password_status = "已设置" if cls.REDIS_PASSWORD and cls.REDIS_PASSWORD.strip() else "未设置"
        print(f"Redis密码: {password_status}")
        print(f"Redis超时: {cls.REDIS_SOCKET_TIMEOUT}秒")
        print(f"SVN服务器: {cls.SVN_URL}")
        print(f"SVN超时: {cls.SVN_TIMEOUT}秒")
        print(f"API服务: {cls.API_HOST}:{cls.API_PORT}")
        print(f"API重载: {cls.API_RELOAD}")
        print(f"日志目录: {cls.LOG_DIRECTORY}")
        print(f"日志级别: {cls.LOG_LEVEL}")
        print(f"批处理大小: {cls.BATCH_SIZE}")
        print("==================")

# 创建全局配置实例
config = Config()

# 环境配置类 - 针对不同部署环境
class EnvironmentConfig:
    """环境特定配置"""
    
    @staticmethod
    def development():
        """开发环境配置"""
        return {
            "API_RELOAD": "true",
            "LOG_LEVEL": "DEBUG"
        }
    
    @staticmethod 
    def production():
        """生产环境配置"""
        return {
            "API_RELOAD": "false",
            "LOG_LEVEL": "INFO",
            "API_HOST": "0.0.0.0"
        }
    
    @staticmethod
    def testing():
        """测试环境配置"""
        return {
            "API_RELOAD": "false",
            "LOG_LEVEL": "DEBUG"
        } 