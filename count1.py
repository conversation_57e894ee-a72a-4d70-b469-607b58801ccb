import re

# 从 redis-res.txt 中提取 filelist 对应的文件路径
redis_files = set()

def decode_if_needed(s):
    # 检查字符串中是否包含类似于 \x 的字节编码
    if '\\x' in s:
        try:
            # 将字符串中的 '\\x' 转换为实际的 '\x'，再解码为 utf-8
            return bytes(s, "utf-8").decode("unicode_escape").encode('latin1').decode('utf-8')
        except (UnicodeDecodeError, ValueError):
            return s  # 如果解码失败，返回原始字符串
    return s


with open('redisres.txt', 'r', encoding='utf-8') as f:
    redis_lines = f.readlines()
    for i in range(len(redis_lines)):
        if '"filelist"' in redis_lines[i]:
            content = '2) "/trunk"'
            # 提取下一行，使用正则表达式去掉多余的部分并去掉 "/trunk"
            match = re.search(r'"\s*/trunk(.*?)"', redis_lines[i + 1])

            if "/trunk" in redis_lines[i + 1]:
                print(1)

            if match and redis_lines[i] != '2) "/trunk"':
                file_path = match.group(1).strip()  # 只保留实际文件路径
                file_path = file_path.replace('/xHelperTray/','')
                # 检查是否含有中文字节编码，如果有就解码
                file_path = decode_if_needed(file_path)

                redis_files.add(file_path)
            else :
                match = re.search(r'"\s*\/(.*?)"', redis_lines[i + 1])
                file_path = match.group(1).strip()
                redis_files.add(file_path)

# 读取 svnlist.txt 文件，并排除目录
svn_files = set()

with open('svnlist.txt', 'r', encoding='utf-8') as f:
    for line in f:
        line = line.strip()
        if not line.endswith('/'):  # 忽略目录
            svn_files.add(line)
        else:
            svn_files.add(line[:-1])

only_in_redis = []


for file in redis_files:

    if file not in svn_files:
        only_in_redis.append(file)


# 输出结果
print(f"svnlist中有但redis中没有的文件（数量：{len(only_in_redis)}）：")
for file in only_in_redis:
    print(file)
