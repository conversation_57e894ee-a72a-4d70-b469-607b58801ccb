import os
import re
import redis

# 定义日志文件所在的目录路径
log_directory = 'G:\logs'  # 替换为你的日志文件目录路径

# 定义正则表达式来匹配日志内容
log_pattern = re.compile(r'(\d{8}-\d{6}),\d+<(\w+)\s*:\d+>:\s*(.*)')

try:
    redis_client = redis.Redis(host='localhost', port=6379, db=0)
    redis_client.ping()
    print("Connected to Redis")
except redis.ConnectionError as e:
    print(f"Could not connect to Redis: {e}")

keys = redis_client.keys("*")
print(keys)