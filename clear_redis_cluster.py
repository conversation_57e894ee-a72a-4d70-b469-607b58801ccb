import redis
from redis.cluster import RedisCluster
from redis.cluster import ClusterNode
import time
import sys

def clear_redis_cluster():
    """
    清空Redis集群中的所有数据
    包含安全确认机制和详细操作日志
    """
    
    # 集群节点配置（与save_json_local.py保持一致）
    startup_nodes = [
        ClusterNode("************", 6010),
        ClusterNode("************", 6002),
        ClusterNode("************", 6003),
    ]
    redis_password = "test123"
    
    print("=" * 60)
    print("🚨 Redis集群数据清理工具 🚨")
    print("=" * 60)
    print(f"目标集群节点:")
    for node in startup_nodes:
        print(f"  - {node.host}:{node.port}")
    print()
    
    # 安全确认机制
    print("⚠️  警告: 此操作将删除集群中的所有数据!")
    print("⚠️  警告: 此操作不可逆转!")
    print()
    
    confirmation = input("请输入 'YES' 确认清空所有数据: ")
    if confirmation != "YES":
        print("❌ 操作已取消")
        return False
    
    print()
    print("🔄 开始连接Redis集群...")
    
    try:
        # 创建Redis集群连接
        redis_client = RedisCluster(
            startup_nodes=startup_nodes, 
            decode_responses=True,
            skip_full_coverage_check=True  # 允许部分节点连接
        )
        
        # 测试连接
        redis_client.ping()
        print("✅ 成功连接到Redis集群")
        
    except redis.ConnectionError as e:
        print(f"❌ 连接Redis集群失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 连接过程中发生错误: {e}")
        return False
    
    try:
        # 获取集群信息
        print("\n📊 集群状态检查...")
        cluster_info = redis_client.cluster_info()
        print(f"集群状态: {cluster_info.get('cluster_state', 'unknown')}")
        
        # 统计当前数据量
        print("\n📈 统计当前数据...")
        total_keys = 0
        nodes_info = []
        
        # 直接使用配置的节点信息
        for node in startup_nodes:
            try:
                single_node = redis.Redis(
                    host=node.host, 
                    port=node.port, 
                    # password=redis_password,
                    decode_responses=True
                )
                
                # 测试连接
                single_node.ping()
                
                # 统计这个节点的key数量
                db_size = single_node.dbsize()
                total_keys += db_size
                nodes_info.append({
                    'host': node.host,
                    'port': node.port,
                    'keys': db_size
                })
                
                print(f"  节点 {node.host}:{node.port} - {db_size} 个key")
                
            except Exception as e:
                print(f"  节点 {node.host}:{node.port} - 统计失败: {e}")
        
        print(f"\n📊 总计: {total_keys} 个key")
        
        if total_keys == 0:
            print("ℹ️  集群中没有数据，无需清理")
            return True
        
        # 最终确认
        print(f"\n⚠️  即将删除 {total_keys} 个key!")
        final_confirmation = input("请再次输入 'DELETE ALL' 确认删除: ")
        if final_confirmation != "DELETE ALL":
            print("❌ 操作已取消")
            return False
        
        print("\n🗑️  开始清理数据...")
        start_time = time.time()
        
        # 对每个主节点执行FLUSHALL
        cleared_nodes = 0
        for node_info in nodes_info:
            try:
                single_node = redis.Redis(
                    host=node_info['host'], 
                    port=node_info['port'], 
                    # password=redis_password,
                    decode_responses=True
                )
                
                # 执行FLUSHALL
                single_node.flushall()
                cleared_nodes += 1
                print(f"✅ 节点 {node_info['host']}:{node_info['port']} 清理完成")
                
            except Exception as e:
                print(f"❌ 节点 {node_info['host']}:{node_info['port']} 清理失败: {e}")
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n🎉 清理完成!")
        print(f"   - 清理节点数: {cleared_nodes}/{len(nodes_info)}")
        print(f"   - 清理时间: {duration:.2f} 秒")
        
        # 验证清理结果
        print("\n🔍 验证清理结果...")
        time.sleep(1)  # 等待一下确保数据同步
        
        remaining_keys = 0
        for node_info in nodes_info:
            try:
                single_node = redis.Redis(
                    host=node_info['host'], 
                    port=node_info['port'], 
                    # password=redis_password,
                    decode_responses=True
                )
                
                db_size = single_node.dbsize()
                remaining_keys += db_size
                print(f"  节点 {node_info['host']}:{node_info['port']} - {db_size} 个key")
                
            except Exception as e:
                print(f"  节点 {node_info['host']}:{node_info['port']} - 验证失败: {e}")
        
        if remaining_keys == 0:
            print("✅ 所有数据已成功清理!")
            return True
        else:
            print(f"⚠️  仍有 {remaining_keys} 个key未清理")
            return False
            
    except Exception as e:
        print(f"❌ 清理过程中发生错误: {e}")
        return False
    
    finally:
        try:
            redis_client.close()
            print("\n🔌 Redis连接已关闭")
        except:
            pass

def show_help():
    """显示帮助信息"""
    print("Redis集群数据清理工具")
    print("用法: python clear_redis_cluster.py [选项]")
    print()
    print("选项:")
    print("  -h, --help     显示此帮助信息")
    print("  -f, --force    跳过确认直接清理(危险!)")
    print()
    print("示例:")
    print("  python clear_redis_cluster.py")
    print("  python clear_redis_cluster.py --help")

def main():
    """主函数"""
    
    # 处理命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] in ['-h', '--help']:
            show_help()
            return
        elif sys.argv[1] in ['-f', '--force']:
            print("⚠️  强制模式暂未实现，为了安全考虑")
            return
    
    # 执行清理
    try:
        success = clear_redis_cluster()
        if success:
            print("\n🎯 清理任务完成，可以开始重新测试了!")
        else:
            print("\n❌ 清理任务未完全成功，请检查错误信息")
            
    except KeyboardInterrupt:
        print("\n\n⏹️  用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")

if __name__ == "__main__":
    main() 