# restart_docker_containers.ps1

# 定义要重启的容器名称列表
$containersToRestart = @("my_web_app", "my_database")

Write-Host "--- Docker 容器定时重启脚本开始执行 ---"
Write-Host "当前时间: $(Get-Date)"

foreach ($container in $containersToRestart) {
    Write-Host "尝试重启容器: $container..."
    # 检查容器是否存在且正在运行
    $containerStatus = docker inspect --format="{{.State.Running}}" $container 2>$null

    if ($containerStatus -eq "true") {
        # 容器正在运行，执行重启
        try {
            docker restart $container
            Write-Host "容器 '$container' 重启成功！"
        } catch {
            Write-Host "错误: 无法重启容器 '$container'。错误信息: $($_.Exception.Message)"
        }
    } elseif ($containerStatus -eq "false") {
        Write-Host "容器 '$container' 已停止，尝试启动..."
        try {
            docker start $container
            Write-Host "容器 '$container' 启动成功！"
        } catch {
            Write-Host "错误: 无法启动容器 '$container'。错误信息: $($_.Exception.Message)"
        }
    } else {
        Write-Host "警告: 容器 '$container' 不存在或状态未知。跳过。"
    }
}

Write-Host "--- Docker 容器定时重启脚本执行完毕 ---"
