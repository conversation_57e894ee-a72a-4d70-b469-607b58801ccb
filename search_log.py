import redis
from redis.cluster import RedisCluster
from redis.cluster import ClusterNode
import json
from typing import List, Dict, Any

class LogSearcher:
    def __init__(self):
        # 定义集群节点
        self.startup_nodes = [
            ClusterNode("10.11.184.46", 6010),
            ClusterNode("10.11.184.46", 6002),
            ClusterNode("10.11.184.46", 6003),
        ]
        self.redis_password = "test123"
        self.redis_client = None
        self.connect_to_redis()

    def connect_to_redis(self):
        """连接到Redis集群"""
        try:
            self.redis_client = RedisCluster(
                startup_nodes=self.startup_nodes,
                decode_responses=True
            )
            self.redis_client.ping()
            print("成功连接到Redis集群")
        except redis.ConnectionError as e:
            print(f"无法连接到Redis集群: {e}")
            raise

    def search_by_version(self, version: int, limit: int = 100) -> List[Dict[Any, Any]]:
        """
        根据版本号搜索日志
        
        Args:
            version: 要搜索的版本号
            limit: 返回结果的最大数量
            
        Returns:
            包含搜索结果的列表
        """
        try:
            # 构建查询
            query = f"@version:{version}"
            index_name = "my_index"
            
            # 执行搜索
            result = self.redis_client.execute_command(
                'FT.SEARCH',
                index_name,
                query,
                'LIMIT', '0', str(limit)
            )
            
            # 处理搜索结果
            if not result or len(result) == 0:
                print(f"未找到版本号为 {version} 的日志")
                return []
            
            total_results = result[0]
            print(f"找到 {total_results} 条版本号为 {version} 的日志记录")
            
            # 解析结果
            documents = []
            for i in range(1, len(result), 2):
                if i + 1 < len(result):
                    doc_id = result[i]
                    doc_data = result[i + 1]
                    
                    # 将Redis返回的数据转换为字典格式
                    doc_dict = {}
                    for j in range(0, len(doc_data), 2):
                        if j + 1 < len(doc_data):
                            key = doc_data[j].replace('$', '')  # 移除JSON路径中的$符号
                            try:
                                value = json.loads(doc_data[j + 1])
                            except json.JSONDecodeError:
                                value = doc_data[j + 1]
                            doc_dict[key] = value
                    
                    documents.append({
                        'id': doc_id,
                        'data': doc_dict
                    })
            
            return documents
            
        except Exception as e:
            print(f"搜索过程中出错: {e}")
            return []

    def print_log_details(self, documents: List[Dict[Any, Any]]):
        """
        打印日志详细信息
        
        Args:
            documents: 搜索结果文档列表
        """
        for doc in documents:
            print("\n" + "="*50)
            print(f"文档ID: {doc['id']}")
            print("日志内容:")
            data = doc['data']
            for key, value in data.items():
                print(f"{key}: {value}")

def main():
    # 创建搜索器实例
    searcher = LogSearcher()
    
    try:
        # 搜索版本号为5281的日志
        version = 5281
        results = searcher.search_by_version(version)
        
        # 打印结果
        if results:
            searcher.print_log_details(results)
        else:
            print("未找到匹配的日志记录")
            
    except Exception as e:
        print(f"程序执行出错: {e}")

if __name__ == "__main__":
    main()