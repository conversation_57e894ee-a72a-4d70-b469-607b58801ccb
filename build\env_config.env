# 生产环境配置文件示例
# 复制此文件并根据生产环境修改相应的配置值

# Redis 集群配置 (请修改为实际的生产环境地址)
REDIS_HOST_1=************
REDIS_PORT_1=6010
REDIS_HOST_2=************
REDIS_PORT_2=6002
REDIS_HOST_3=************
REDIS_PORT_3=6003
# REDIS_PASSWORD=your_production_password  # 生产环境请设置密码
REDIS_SOCKET_TIMEOUT=3
REDIS_SOCKET_KEEPALIVE=true

# SVN 服务器配置 (请修改为实际的SVN服务器地址)
SVN_URL=yexiao.fun
SVN_TIMEOUT=300

# API 服务配置
API_HOST=0.0.0.0
API_PORT=5000
API_RELOAD=false

# 文件路径配置 (生产环境路径)
LOG_DIRECTORY=/var/log/svn_logs

# 性能配置
BATCH_SIZE=1000

# 日志配置
LOG_LEVEL=INFO

# 部署环境标识
ENVIRONMENT=production 