# 🚀 SVN Log Writer API 生产环境部署指南

## 📋 部署清单

### 🔧 项目文件结构
```
项目根目录:
├── api_service.py         # 主API服务程序
├── save_json_local.py     # Redis操作和日志处理核心模块
├── get_info_xml.py        # XML解析工具模块
├── config.py              # 统一配置管理 (新增)
├── requirements.txt       # Python依赖包清单
├── env_config.env         # 当前环境配置文件 (新增)
├── production.env         # 生产环境配置模板 (新增)
├── test_redis_connection.py  # Redis连接诊断工具 (新增)
├── build_deployment.py    # 部署构建脚本 (新增)
├── DEPLOYMENT_GUIDE.md    # 本部署指南 (新增)
└── build/                 # 构建脚本生成的部署包
    ├── api_service.py
    ├── save_json_local.py
    ├── get_info_xml.py
    ├── config.py
    ├── requirements.txt
    ├── env_config.env     # 从production.env复制
    ├── production.env
    ├── test_redis_connection.py
    ├── DEPLOYMENT_GUIDE.md
    ├── start_service.sh   # Linux/Mac启动脚本
    ├── start_service.bat  # Windows启动脚本
    └── DEPLOYMENT_README.md  # 部署说明
```

### 📦 Python依赖包
```txt
fastapi==0.104.1
uvicorn==0.24.0
redis==4.6.0
redis-py-cluster==2.1.3
requests==2.31.0
```

## 🔧 配置管理

### 🌍 配置文件管理
新的配置系统直接从env_config.env文件读取所有配置项，无需修改代码：

```bash
# 开发环境 - 直接使用默认配置文件
vim env_config.env

# 生产环境 - 复制并修改配置文件
cp production.env env_config.env
vim env_config.env
```

### 📝 配置项说明

#### Redis集群配置
```bash
REDIS_HOST_1=************      # Redis节点1主机
REDIS_PORT_1=6010              # Redis节点1端口
REDIS_HOST_2=************      # Redis节点2主机 
REDIS_PORT_2=6002              # Redis节点2端口
REDIS_HOST_3=************      # Redis节点3主机
REDIS_PORT_3=6003              # Redis节点3端口
REDIS_PASSWORD=test123          # Redis密码
REDIS_SOCKET_TIMEOUT=3          # 连接超时时间(秒)
REDIS_SOCKET_KEEPALIVE=true     # 保持连接活跃
```

#### SVN服务器配置
```bash
SVN_URL=yexiao.fun             # SVN服务器地址
SVN_TIMEOUT=300                # SVN命令超时时间(秒)
```

#### API服务配置
```bash
API_HOST=0.0.0.0               # API监听地址
API_PORT=5000                  # API监听端口
API_RELOAD=false               # 生产环境关闭热重载
```

#### 其他配置
```bash
LOG_DIRECTORY=/var/log/svn_logs  # 日志文件目录
BATCH_SIZE=1000                  # 批处理大小
LOG_LEVEL=INFO                   # 日志级别
ENVIRONMENT=production           # 环境标识
```

## 🚀 快速部署 (推荐)

### 📦 使用构建脚本一键打包
```bash
# 运行构建脚本
python build_deployment.py

# 将build文件夹复制到生产服务器
scp -r build/ user@production-server:/path/to/deployment/

# 在生产服务器上启动服务
cd /path/to/deployment/build/
./start_service.sh          # Linux/Mac
# 或
start_service.bat           # Windows
```

这样就完成了部署！构建脚本会自动：
- ✅ 复制所有必需文件
- ✅ 创建配置文件模板  
- ✅ 生成启动脚本
- ✅ 创建部署说明文档

## 🏗️ 手动部署步骤

### 1️⃣ 环境准备
```bash
# 安装Python 3.8+
python3 --version

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2️⃣ 配置文件设置
```bash
# 复制生产环境配置模板
cp production.env env_config.env

# 根据生产环境修改配置
vim env_config.env
```

### 3️⃣ 服务启动
```bash
# 直接启动
python api_service.py

# 或使用更强大的WSGI服务器
pip install gunicorn
gunicorn -w 4 -k uvicorn.workers.UvicornWorker api_service:app --bind 0.0.0.0:5000
```

### 4️⃣ 系统服务配置 (Linux)
创建systemd服务文件 `/etc/systemd/system/svn-log-api.service`:

```ini
[Unit]
Description=SVN Log Writer API
After=network.target

[Service]
Type=simple
User=your-user
WorkingDirectory=/path/to/your/project
Environment=PATH=/path/to/your/project/venv/bin
ExecStart=/path/to/your/project/venv/bin/python api_service.py
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
```

启动系统服务:
```bash
sudo systemctl daemon-reload
sudo systemctl enable svn-log-api
sudo systemctl start svn-log-api
sudo systemctl status svn-log-api
```

## 🔍 配置验证

### ✅ 验证配置加载
在Python中验证配置是否正确加载:

```python
from config import config

# 打印当前配置
config.print_config()

# 验证Redis连接
from save_json_local import redis_client
redis_client.ping()
```

### 🔗 API端点测试
```bash
# 检查服务状态
curl http://localhost:5000/status

# 查看API文档
curl http://localhost:5000/docs

# 测试日志更新
curl -X POST "http://localhost:5000/update_log?url=yexiao.fun"
```

## 🛡️ 安全建议

### 🔐 敏感信息保护
- Redis密码等敏感信息使用环境变量存储
- 生产环境配置文件权限设为 600
- 不要将包含密码的配置文件提交到版本控制

### 🔥 防火墙配置
```bash
# 仅开放必要端口
sudo ufw allow 5000/tcp  # API端口
sudo ufw allow ssh       # SSH访问
```

### 📊 监控配置
- 配置日志轮转
- 设置磁盘空间监控
- 配置服务健康检查

## 🐛 故障排查

### ⚠️ Redis连接错误 (AUTH失败)
如果遇到以下错误：
```
RedisClusterException: AUTH <password> called without any password configured
```

**解决步骤**：
```bash
# 1. 运行Redis连接诊断工具
python test_redis_connection.py

# 2. 根据诊断结果调整配置
# 编辑配置文件env_config.env，注释掉密码行:
vim env_config.env
# REDIS_PASSWORD=test123

# 重新启动服务
python api_service.py
```

### 常见问题
1. **Redis连接失败**: 
   - 运行 `python test_redis_connection.py` 诊断
   - 检查REDIS_HOST和REDIS_PORT配置
   - 验证Redis服务是否运行
2. **Redis认证失败**: 
   - 使用诊断工具确认密码配置
   - 确认Redis服务器密码设置
3. **SVN命令失败**: 检查SVN_URL配置和网络连接
4. **权限错误**: 检查LOG_DIRECTORY路径权限
5. **端口占用**: 检查API_PORT是否被其他进程占用

### 🔧 调试工具
```bash
# Redis连接诊断
python test_redis_connection.py

# 手动测试Redis连接
redis-cli -h ************ -p 6010 ping

# 检查Redis集群状态
redis-cli -h ************ -p 6010 cluster nodes
```

### 日志查看
```bash
# 查看系统服务日志
sudo journalctl -u svn-log-api -f

# 查看应用日志
tail -f /var/log/svn_logs/app.log
```

## 📈 性能优化

### 配置调优
- `BATCH_SIZE`: 根据内存大小调整批处理数量
- `SVN_TIMEOUT`: 根据网络延迟调整超时时间
- `REDIS_SOCKET_TIMEOUT`: 根据Redis性能调整

### 扩展性
- 支持多Redis节点集群
- 支持水平扩展API服务
- 支持负载均衡配置

## 🔄 版本升级

### 升级步骤
1. 备份当前配置文件
2. 停止服务
3. 更新代码文件
4. 检查配置兼容性
5. 启动服务
6. 验证功能正常

### 回滚方案
- 保留上一版本的完整备份
- 配置文件版本控制
- 数据库备份策略 