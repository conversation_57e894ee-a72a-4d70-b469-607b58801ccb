#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生产环境部署构建脚本
将所有必需的文件复制到build文件夹，便于部署到生产环境
"""

import os
import shutil
import sys
from datetime import datetime

# 定义需要复制的文件列表
REQUIRED_FILES = [
    "api_service.py",              # 主API服务程序
    "save_json_local.py",          # Redis操作和日志处理核心模块
    "get_info_xml.py",             # XML解析工具模块
    "config.py",                   # 统一配置管理
    "requirements.txt",            # Python依赖包清单
    "production.env",              # 生产环境配置模板
    "test_redis_connection.py",    # Redis连接诊断工具
    "DEPLOYMENT_GUIDE.md",         # 部署指南
]

# 可选文件 (如果存在则复制)
OPTIONAL_FILES = [
    "README.md",
    "env_config.env",              # 如果有开发环境配置也复制过去作为参考
]

def check_file_exists(filepath):
    """检查文件是否存在"""
    if not os.path.exists(filepath):
        print(f"❌ 文件不存在: {filepath}")
        return False
    return True

def create_build_directory(build_dir="build"):
    """创建构建目录"""
    if os.path.exists(build_dir):
        print(f"🗑️  清理现有构建目录: {build_dir}")
        shutil.rmtree(build_dir)
    
    os.makedirs(build_dir)
    print(f"📁 创建构建目录: {build_dir}")
    return build_dir

def copy_files(files, build_dir, required=True):
    """复制文件到构建目录"""
    copied_files = []
    missing_files = []
    
    for filename in files:
        if os.path.exists(filename):
            dest_path = os.path.join(build_dir, filename)
            shutil.copy2(filename, dest_path)
            print(f"✅ 复制文件: {filename}")
            copied_files.append(filename)
        else:
            if required:
                print(f"❌ 必需文件缺失: {filename}")
                missing_files.append(filename)
            else:
                print(f"⚠️  可选文件不存在: {filename}")
    
    return copied_files, missing_files

def create_production_config(build_dir):
    """在构建目录中创建生产环境配置文件"""
    production_env_path = os.path.join(build_dir, "production.env")
    env_config_path = os.path.join(build_dir, "env_config.env")
    
    if os.path.exists(production_env_path):
        # 复制production.env为env_config.env
        shutil.copy2(production_env_path, env_config_path)
        print(f"📝 创建默认配置文件: env_config.env (从 production.env 复制)")
    else:
        print(f"⚠️  production.env 文件不存在，请手动创建配置文件")

def create_startup_script(build_dir):
    """创建启动脚本"""
    startup_script_content = """#!/bin/bash
# 生产环境启动脚本

# 检查Python虚拟环境
if [ ! -d "venv" ]; then
    echo "🔧 创建Python虚拟环境..."
    python3 -m venv venv
fi

# 激活虚拟环境
source venv/bin/activate

# 安装依赖
echo "📦 安装Python依赖..."
pip install -r requirements.txt

# 检查配置文件
if [ ! -f "env_config.env" ]; then
    echo "⚠️  配置文件不存在，使用默认配置..."
    cp production.env env_config.env
    echo "💡 请编辑 env_config.env 文件以设置正确的配置"
fi

# 运行Redis连接测试
echo "🔍 测试Redis连接..."
python test_redis_connection.py

# 启动API服务
echo "🚀 启动API服务..."
python api_service.py
"""

    # Windows启动脚本
    startup_bat_content = """@echo off
REM 生产环境启动脚本 (Windows)

REM 检查Python虚拟环境
if not exist "venv" (
    echo 🔧 创建Python虚拟环境...
    python -m venv venv
)

REM 激活虚拟环境
call venv\\Scripts\\activate.bat

REM 安装依赖
echo 📦 安装Python依赖...
pip install -r requirements.txt

REM 检查配置文件
if not exist "env_config.env" (
    echo ⚠️  配置文件不存在，使用默认配置...
    copy production.env env_config.env
    echo 💡 请编辑 env_config.env 文件以设置正确的配置
)

REM 运行Redis连接测试
echo 🔍 测试Redis连接...
python test_redis_connection.py

REM 启动API服务
echo 🚀 启动API服务...
python api_service.py

pause
"""

    # 创建Linux/Mac启动脚本
    startup_sh_path = os.path.join(build_dir, "start_service.sh")
    with open(startup_sh_path, 'w', encoding='utf-8') as f:
        f.write(startup_script_content)
    
    # 设置执行权限
    os.chmod(startup_sh_path, 0o755)
    print(f"📜 创建启动脚本: start_service.sh")
    
    # 创建Windows启动脚本
    startup_bat_path = os.path.join(build_dir, "start_service.bat")
    with open(startup_bat_path, 'w', encoding='utf-8') as f:
        f.write(startup_bat_content)
    print(f"📜 创建启动脚本: start_service.bat")

def create_readme(build_dir):
    """创建部署说明文件"""
    readme_content = f"""# SVN Log Writer API - 生产环境部署包

## 📋 包含文件
- api_service.py: 主API服务程序
- save_json_local.py: Redis操作和日志处理核心模块
- get_info_xml.py: XML解析工具模块
- config.py: 统一配置管理
- requirements.txt: Python依赖包清单
- env_config.env: 环境配置文件
- production.env: 生产环境配置模板
- test_redis_connection.py: Redis连接诊断工具
- DEPLOYMENT_GUIDE.md: 详细部署指南
- start_service.sh: Linux/Mac启动脚本
- start_service.bat: Windows启动脚本

## 🚀 快速部署

### Linux/Mac:
```bash
chmod +x start_service.sh
./start_service.sh
```

### Windows:
```cmd
start_service.bat
```

## ⚙️ 手动部署

1. 安装Python 3.8+
2. 创建虚拟环境: `python3 -m venv venv`
3. 激活虚拟环境: `source venv/bin/activate` (Linux/Mac) 或 `venv\\Scripts\\activate` (Windows)
4. 安装依赖: `pip install -r requirements.txt`
5. 编辑配置文件: `vim env_config.env`
6. 测试连接: `python test_redis_connection.py`
7. 启动服务: `python api_service.py`

## 📝 重要提醒

1. **配置文件**: 请根据生产环境修改 `env_config.env` 中的配置
2. **Redis连接**: 启动前请确保Redis集群可访问
3. **SVN服务**: 请确保SVN服务器地址正确
4. **端口开放**: 确保API端口(默认5000)未被占用
5. **日志目录**: 确保LOG_DIRECTORY路径存在且有写入权限

## 🔗 API端点

- 服务状态: http://localhost:5000/status
- API文档: http://localhost:5000/docs
- 更新日志: POST http://localhost:5000/update_log

构建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

    readme_path = os.path.join(build_dir, "DEPLOYMENT_README.md")
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print(f"📖 创建部署说明: DEPLOYMENT_README.md")

def validate_build(build_dir, required_files):
    """验证构建结果"""
    print(f"\n🔍 验证构建结果...")
    
    all_files_exist = True
    for filename in required_files:
        file_path = os.path.join(build_dir, filename)
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            print(f"✅ {filename} ({file_size} bytes)")
        else:
            print(f"❌ {filename} 缺失")
            all_files_exist = False
    
    return all_files_exist

def main():
    """主函数"""
    print("🚀 开始构建生产环境部署包...")
    print("=" * 50)
    
    # 1. 检查必需文件是否存在
    print("📋 检查必需文件...")
    missing_files = []
    for filename in REQUIRED_FILES:
        if not check_file_exists(filename):
            missing_files.append(filename)
    
    if missing_files:
        print(f"\n❌ 构建失败！缺少必需文件: {', '.join(missing_files)}")
        print("💡 请确保在项目根目录运行此脚本，并且所有文件都存在")
        sys.exit(1)
    
    print("✅ 所有必需文件检查通过")
    
    # 2. 创建构建目录
    print(f"\n📁 创建构建目录...")
    build_dir = create_build_directory()
    
    # 3. 复制必需文件
    print(f"\n📂 复制必需文件...")
    copied_required, missing_required = copy_files(REQUIRED_FILES, build_dir, required=True)
    
    if missing_required:
        print(f"\n❌ 构建失败！缺少必需文件: {', '.join(missing_required)}")
        sys.exit(1)
    
    # 4. 复制可选文件
    print(f"\n📂 复制可选文件...")
    copied_optional, _ = copy_files(OPTIONAL_FILES, build_dir, required=False)
    
    # 5. 创建生产环境配置
    print(f"\n⚙️ 配置生产环境...")
    create_production_config(build_dir)
    
    # 6. 创建启动脚本
    print(f"\n📜 创建启动脚本...")
    create_startup_script(build_dir)
    
    # 7. 创建部署说明
    print(f"\n📖 创建部署说明...")
    create_readme(build_dir)
    
    # 8. 验证构建结果
    if validate_build(build_dir, REQUIRED_FILES):
        print(f"\n🎉 构建成功！")
        print(f"📦 部署包位置: {os.path.abspath(build_dir)}")
        print(f"📋 包含文件: {len(copied_required) + len(copied_optional) + 4} 个")
        print(f"\n💡 下一步:")
        print(f"   1. 将 {build_dir} 文件夹复制到生产服务器")
        print(f"   2. 在生产服务器上运行: ./start_service.sh (Linux/Mac) 或 start_service.bat (Windows)")
        print(f"   3. 根据需要修改 env_config.env 配置文件")
    else:
        print(f"\n❌ 构建验证失败！")
        sys.exit(1)

if __name__ == "__main__":
    main() 